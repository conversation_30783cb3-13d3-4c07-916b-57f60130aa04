<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الحاسبات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .back-btn {
            background: #e74c3c;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .back-btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .computers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            padding: 30px;
        }

        .computer-card {
            background: white;
            border: 2px solid #ecf0f1;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .computer-card:hover {
            border-color: #3498db;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.2);
        }

        .computer-card.active {
            border-color: #2ecc71;
            background: #f8fff9;
        }

        .computer-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .computer-icon {
            font-size: 2.5em;
            margin-left: 15px;
            color: #3498db;
        }

        .computer-info h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.4em;
        }

        .computer-info p {
            margin: 5px 0 0 0;
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .computer-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
            display: block;
        }

        .stat-label {
            font-size: 0.8em;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .computer-details {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #3498db;
        }

        .computer-details.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .permissions-section {
            margin-top: 20px;
        }

        .permissions-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #ecf0f1;
        }

        .permission-item input[type="checkbox"] {
            margin-left: 10px;
            transform: scale(1.2);
        }

        .permission-item label {
            font-size: 0.9em;
            color: #2c3e50;
            cursor: pointer;
        }

        .update-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 15px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .update-btn:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .password-btn {
            background: #3498db;
        }

        .password-btn:hover {
            background: #2980b9;
        }

        .password-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .password-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 15px 0;
        }

        .password-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .password-item label {
            font-weight: bold;
            color: #2c3e50;
            font-size: 0.9em;
        }

        .password-item input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9em;
            background: white;
        }

        .password-item input[readonly] {
            background: #f8f9fa;
            color: #6c757d;
        }

        /* منع التمرير غير المرغوب فيه */
        .computer-details * {
            scroll-behavior: auto;
        }

        .permission-item {
            position: relative;
        }

        .status-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-active {
            background: #d5f4e6;
            color: #27ae60;
        }

        .status-inactive {
            background: #fadbd8;
            color: #e74c3c;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
        }

        .no-computers {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/admin/dashboard" class="back-btn">🔙 العودة لوحة التحكم</a>
            <h1>💻 قائمة الحاسبات المسجلة</h1>
        </div>

        <div class="computers-grid" id="computersGrid">
            <div class="loading">
                <div>⏳ جاري تحميل البيانات...</div>
            </div>
        </div>
    </div>

    <script>
        let computers = {};
        let activeCard = null;

        // تحميل بيانات الحاسبات
        async function loadComputers() {
            try {
                const response = await fetch('/admin/api/computers');
                const data = await response.json();
                
                if (data.success) {
                    computers = data.computers;
                    renderComputers();
                } else {
                    showError('فشل في تحميل بيانات الحاسبات');
                }
            } catch (error) {
                console.error('Error loading computers:', error);
                showError('خطأ في الاتصال بالخادم');
            }
        }

        // عرض الحاسبات
        function renderComputers() {
            const grid = document.getElementById('computersGrid');
            
            if (Object.keys(computers).length === 0) {
                grid.innerHTML = '<div class="no-computers">لا توجد حاسبات مسجلة</div>';
                return;
            }

            let html = '';
            Object.entries(computers).forEach(([name, data]) => {
                const hasContract = data.has_saved_contract;
                const statusClass = hasContract ? 'status-active' : 'status-inactive';
                const statusText = hasContract ? 'نشط' : 'غير نشط';
                
                html += `
                    <div class="computer-card" onclick="toggleDetails('${name}', event)">
                        <div class="status-badge ${statusClass}">${statusText}</div>
                        
                        <div class="computer-header">
                            <div class="computer-icon">💻</div>
                            <div class="computer-info">
                                <h3>${name}</h3>
                                <p>ID: ${data.id}</p>
                            </div>
                        </div>

                        <div class="computer-stats">
                            <div class="stat-item">
                                <span class="stat-value">${data.contracts_count || 0}</span>
                                <div class="stat-label">عدد العقود</div>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${hasContract ? '✓' : '✗'}</span>
                                <div class="stat-label">عقد محفوظ</div>
                            </div>
                        </div>

                        <div class="computer-details" id="details_${name}">
                            <div><strong>تاريخ التسجيل:</strong> ${data.created_at}</div>
                            
                            <div class="permissions-section">
                                <div class="permissions-title">🔐 الصلاحيات</div>
                                <div class="permissions-grid">
                                    <div class="permission-item">
                                        <input type="checkbox" id="edit_finalized_${name}" ${data.permissions?.edit_finalized_contracts ? 'checked' : ''} onclick="event.stopPropagation()">
                                        <label for="edit_finalized_${name}" onclick="event.stopPropagation()">تعديل العقود المبرمة</label>
                                    </div>
                                    <div class="permission-item">
                                        <input type="checkbox" id="download_finalized_${name}" ${data.permissions?.download_finalized_contracts ? 'checked' : ''} onclick="event.stopPropagation()">
                                        <label for="download_finalized_${name}" onclick="event.stopPropagation()">تحميل العقود المبرمة</label>
                                    </div>
                                </div>
                                
                                <button class="update-btn" onclick="updatePermissions('${name}', event)">
                                    💾 تحديث الصلاحيات
                                </button>
                            </div>

                            <div class="password-section">
                                <div class="permissions-title">🔑 إدارة كلمة المرور</div>
                                <div class="password-grid">
                                    <div class="password-item">
                                        <label>كلمة المرور الحالية:</label>
                                        <input type="text" id="current_password_${name}" value="${data.password || ''}" readonly onclick="event.stopPropagation()">
                                    </div>
                                    <div class="password-item">
                                        <label>كلمة المرور الجديدة:</label>
                                        <input type="password" id="new_password_${name}" placeholder="أدخل كلمة المرور الجديدة" onclick="event.stopPropagation()">
                                    </div>
                                </div>

                                <button class="update-btn password-btn" onclick="updatePassword('${name}', event)">
                                    🔐 تحديث كلمة المرور
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            grid.innerHTML = html;
        }

        // تبديل عرض التفاصيل
        function toggleDetails(computerName, event) {
            // التحقق من أن النقر ليس على عنصر تفاعلي
            if (event && (
                event.target.matches('input') ||
                event.target.matches('button') ||
                event.target.matches('label') ||
                event.target.closest('.update-btn') ||
                event.target.closest('.password-item') ||
                event.target.closest('.permission-item')
            )) {
                return; // لا تفعل شيئاً إذا كان النقر على عنصر تفاعلي
            }

            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const card = event ? event.currentTarget : document.querySelector(`[onclick*="${computerName}"]`);
            const details = document.getElementById(`details_${computerName}`);

            if (!card || !details) return;

            // إغلاق البطاقة النشطة السابقة
            if (activeCard && activeCard !== card) {
                activeCard.classList.remove('active');
                const prevDetails = activeCard.querySelector('.computer-details');
                if (prevDetails) {
                    prevDetails.classList.remove('show');
                }
            }

            // تبديل البطاقة الحالية
            const isCurrentlyActive = card.classList.contains('active');

            if (isCurrentlyActive) {
                card.classList.remove('active');
                details.classList.remove('show');
                activeCard = null;
            } else {
                card.classList.add('active');
                details.classList.add('show');
                activeCard = card;
            }
        }

        // تحديث صلاحيات حاسوب معين
        async function updatePermissions(computerName, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation(); // منع تفعيل toggleDetails
            }

            const permissions = {
                edit_finalized_contracts: document.getElementById(`edit_finalized_${computerName}`).checked,
                download_finalized_contracts: document.getElementById(`download_finalized_${computerName}`).checked
            };

            try {
                const response = await fetch('/admin/api/permissions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        computer_name: computerName,
                        permissions: permissions
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    alert('تم تحديث الصلاحيات بنجاح');
                    // تحديث البيانات المحلية
                    computers[computerName].permissions = permissions;
                } else {
                    alert('خطأ في تحديث الصلاحيات: ' + data.message);
                }
            } catch (error) {
                console.error('Error updating permissions:', error);
                alert('خطأ في تحديث الصلاحيات');
            }
        }

        // تحديث كلمة مرور حاسوب معين
        async function updatePassword(computerName, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation(); // منع تفعيل toggleDetails
            }

            const newPassword = document.getElementById(`new_password_${computerName}`).value.trim();

            if (!newPassword) {
                alert('يرجى إدخال كلمة المرور الجديدة');
                return;
            }

            if (newPassword.length < 3) {
                alert('كلمة المرور يجب أن تكون 3 أحرف على الأقل');
                return;
            }

            try {
                const response = await fetch('/admin/api/computer/password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        computer_name: computerName,
                        new_password: newPassword
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('تم تحديث كلمة المرور بنجاح');
                    // تحديث البيانات المحلية
                    computers[computerName].password = newPassword;
                    // تحديث الحقل المعروض
                    document.getElementById(`current_password_${computerName}`).value = newPassword;
                    // مسح حقل كلمة المرور الجديدة
                    document.getElementById(`new_password_${computerName}`).value = '';
                } else {
                    alert('خطأ في تحديث كلمة المرور: ' + data.message);
                }
            } catch (error) {
                console.error('Error updating password:', error);
                alert('خطأ في تحديث كلمة المرور');
            }
        }

        // عرض رسالة خطأ
        function showError(message) {
            const grid = document.getElementById('computersGrid');
            grid.innerHTML = `<div class="no-computers">❌ ${message}</div>`;
        }

        // منع التمرير غير المرغوب فيه
        function preventScroll(event) {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadComputers();

            // منع التمرير عند النقر على العناصر التفاعلية
            document.addEventListener('click', function(e) {
                if (e.target.matches('input[type="checkbox"]') ||
                    e.target.matches('input[type="password"]') ||
                    e.target.matches('input[type="text"]') ||
                    e.target.matches('label') ||
                    e.target.matches('.update-btn')) {
                    e.stopPropagation();
                }
            });
        });
    </script>
</body>
</html>
