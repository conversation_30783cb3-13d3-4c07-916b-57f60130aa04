<!DOCTYPE html>
<!-- saved from url=(0048)https://itpec.ur.gov.iq/Offices/Contracts/Create -->
<html lang="ar" dir="rtl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create </title>

    <!--bootstrap-->
    <link rel="stylesheet" href="./Create_files/bootstrap.min.css">
    <!--normlize-->
    <link rel="stylesheet" href="./Create_files/normlize.css">
    <!--dataTable-->
    <link rel="stylesheet" href="./Create_files/dataTables.min.css">
    <!--font awesome-->
    <link rel="stylesheet" href="./Create_files/all.min.css">
    <!--gijgo-->
    <link rel="stylesheet" href="./Create_files/gijgo.min.css">
    <!--select2-->
    <link rel="stylesheet" href="./Create_files/select2.min.css">
    <!--css file-->
    <link rel="stylesheet" href="./Create_files/daterangepicker.css">
    <link rel="stylesheet" href="./Create_files/global.min.css">
    <link href="./Create_files/bootstrap-icons.css" rel="stylesheet">
    <link href="./Create_files/bootstrap-icons.min.css" rel="stylesheet">

<style>.swal2-popup.swal2-toast{box-sizing:border-box;grid-column:1/4!important;grid-row:1/4!important;grid-template-columns:1fr 99fr 1fr;padding:1em;overflow-y:hidden;background:#fff;box-shadow:0 0 1px rgba(0,0,0,.075),0 1px 2px rgba(0,0,0,.075),1px 2px 4px rgba(0,0,0,.075),1px 3px 8px rgba(0,0,0,.075),2px 4px 16px rgba(0,0,0,.075);pointer-events:all}.swal2-popup.swal2-toast>*{grid-column:2}.swal2-popup.swal2-toast .swal2-title{margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-popup.swal2-toast .swal2-loading{justify-content:center}.swal2-popup.swal2-toast .swal2-input{height:2em;margin:.5em;font-size:1em}.swal2-popup.swal2-toast .swal2-validation-message{font-size:1em}.swal2-popup.swal2-toast .swal2-footer{margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-popup.swal2-toast .swal2-close{grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-popup.swal2-toast .swal2-html-container{margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-popup.swal2-toast .swal2-html-container:empty{padding:0}.swal2-popup.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-popup.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-popup.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:700}.swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-popup.swal2-toast .swal2-actions{justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-popup.swal2-toast .swal2-styled{margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-popup.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;transform:rotate(45deg);border-radius:50%}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-.8em;left:-.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-popup.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{-webkit-animation:swal2-toast-animate-success-line-tip .75s;animation:swal2-toast-animate-success-line-tip .75s}.swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{-webkit-animation:swal2-toast-animate-success-line-long .75s;animation:swal2-toast-animate-success-line-long .75s}.swal2-popup.swal2-toast.swal2-show{-webkit-animation:swal2-toast-show .5s;animation:swal2-toast-show .5s}.swal2-popup.swal2-toast.swal2-hide{-webkit-animation:swal2-toast-hide .1s forwards;animation:swal2-toast-hide .1s forwards}.swal2-container{display:grid;position:fixed;z-index:1060;top:0;right:0;bottom:0;left:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(-webkit-min-content,auto) minmax(-webkit-min-content,auto) minmax(-webkit-min-content,auto);grid-template-rows:minmax(min-content,auto) minmax(min-content,auto) minmax(min-content,auto);height:100%;padding:.625em;overflow-x:hidden;transition:background-color .1s;-webkit-overflow-scrolling:touch}.swal2-container.swal2-backdrop-show,.swal2-container.swal2-noanimation{background:rgba(0,0,0,.4)}.swal2-container.swal2-backdrop-hide{background:0 0!important}.swal2-container.swal2-bottom-start,.swal2-container.swal2-center-start,.swal2-container.swal2-top-start{grid-template-columns:minmax(0,1fr) auto auto}.swal2-container.swal2-bottom,.swal2-container.swal2-center,.swal2-container.swal2-top{grid-template-columns:auto minmax(0,1fr) auto}.swal2-container.swal2-bottom-end,.swal2-container.swal2-center-end,.swal2-container.swal2-top-end{grid-template-columns:auto auto minmax(0,1fr)}.swal2-container.swal2-top-start>.swal2-popup{align-self:start}.swal2-container.swal2-top>.swal2-popup{grid-column:2;align-self:start;justify-self:center}.swal2-container.swal2-top-end>.swal2-popup,.swal2-container.swal2-top-right>.swal2-popup{grid-column:3;align-self:start;justify-self:end}.swal2-container.swal2-center-left>.swal2-popup,.swal2-container.swal2-center-start>.swal2-popup{grid-row:2;align-self:center}.swal2-container.swal2-center>.swal2-popup{grid-column:2;grid-row:2;align-self:center;justify-self:center}.swal2-container.swal2-center-end>.swal2-popup,.swal2-container.swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;align-self:center;justify-self:end}.swal2-container.swal2-bottom-left>.swal2-popup,.swal2-container.swal2-bottom-start>.swal2-popup{grid-column:1;grid-row:3;align-self:end}.swal2-container.swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;justify-self:center;align-self:end}.swal2-container.swal2-bottom-end>.swal2-popup,.swal2-container.swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;align-self:end;justify-self:end}.swal2-container.swal2-grow-fullscreen>.swal2-popup,.swal2-container.swal2-grow-row>.swal2-popup{grid-column:1/4;width:100%}.swal2-container.swal2-grow-column>.swal2-popup,.swal2-container.swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}.swal2-container.swal2-no-transition{transition:none!important}.swal2-popup{display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0,100%);width:32em;max-width:100%;padding:0 0 1.25em;border:none;border-radius:5px;background:#fff;color:#545454;font-family:inherit;font-size:1rem}.swal2-popup:focus{outline:0}.swal2-popup.swal2-loading{overflow-y:hidden}.swal2-title{position:relative;max-width:100%;margin:0;padding:.8em 1em 0;color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word}.swal2-actions{display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:center;width:auto;margin:1.25em auto 0;padding:0}.swal2-actions:not(.swal2-loading) .swal2-styled[disabled]{opacity:.4}.swal2-actions:not(.swal2-loading) .swal2-styled:hover{background-image:linear-gradient(rgba(0,0,0,.1),rgba(0,0,0,.1))}.swal2-actions:not(.swal2-loading) .swal2-styled:active{background-image:linear-gradient(rgba(0,0,0,.2),rgba(0,0,0,.2))}.swal2-loader{display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;-webkit-animation:swal2-rotate-loading 1.5s linear 0s infinite normal;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 transparent #2778c4 transparent}.swal2-styled{margin:.3125em;padding:.625em 1.1em;transition:box-shadow .1s;box-shadow:0 0 0 3px transparent;font-weight:500}.swal2-styled:not([disabled]){cursor:pointer}.swal2-styled.swal2-confirm{border:0;border-radius:.25em;background:initial;background-color:#7066e0;color:#fff;font-size:1em}.swal2-styled.swal2-confirm:focus{box-shadow:0 0 0 3px rgba(112,102,224,.5)}.swal2-styled.swal2-deny{border:0;border-radius:.25em;background:initial;background-color:#dc3741;color:#fff;font-size:1em}.swal2-styled.swal2-deny:focus{box-shadow:0 0 0 3px rgba(220,55,65,.5)}.swal2-styled.swal2-cancel{border:0;border-radius:.25em;background:initial;background-color:#6e7881;color:#fff;font-size:1em}.swal2-styled.swal2-cancel:focus{box-shadow:0 0 0 3px rgba(110,120,129,.5)}.swal2-styled.swal2-default-outline:focus{box-shadow:0 0 0 3px rgba(100,150,200,.5)}.swal2-styled:focus{outline:0}.swal2-styled::-moz-focus-inner{border:0}.swal2-footer{justify-content:center;margin:1em 0 0;padding:1em 1em 0;border-top:1px solid #eee;color:inherit;font-size:1em}.swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto!important;overflow:hidden;border-bottom-right-radius:5px;border-bottom-left-radius:5px}.swal2-timer-progress-bar{width:100%;height:.25em;background:rgba(0,0,0,.2)}.swal2-image{max-width:100%;margin:2em auto 1em}.swal2-close{z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:color .1s,box-shadow .1s;border:none;border-radius:5px;background:0 0;color:#ccc;font-family:serif;font-family:monospace;font-size:2.5em;cursor:pointer;justify-self:end}.swal2-close:hover{transform:none;background:0 0;color:#f27474}.swal2-close:focus{outline:0;box-shadow:inset 0 0 0 3px rgba(100,150,200,.5)}.swal2-close::-moz-focus-inner{border:0}.swal2-html-container{z-index:1;justify-content:center;margin:1em 1.6em .3em;padding:0;overflow:auto;color:inherit;font-size:1.125em;font-weight:400;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word}.swal2-checkbox,.swal2-file,.swal2-input,.swal2-radio,.swal2-select,.swal2-textarea{margin:1em 2em 3px}.swal2-file,.swal2-input,.swal2-textarea{box-sizing:border-box;width:auto;transition:border-color .1s,box-shadow .1s;border:1px solid #d9d9d9;border-radius:.1875em;background:inherit;box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px transparent;color:inherit;font-size:1.125em}.swal2-file.swal2-inputerror,.swal2-input.swal2-inputerror,.swal2-textarea.swal2-inputerror{border-color:#f27474!important;box-shadow:0 0 2px #f27474!important}.swal2-file:focus,.swal2-input:focus,.swal2-textarea:focus{border:1px solid #b4dbed;outline:0;box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(100,150,200,.5)}.swal2-file::-moz-placeholder,.swal2-input::-moz-placeholder,.swal2-textarea::-moz-placeholder{color:#ccc}.swal2-file:-ms-input-placeholder,.swal2-input:-ms-input-placeholder,.swal2-textarea:-ms-input-placeholder{color:#ccc}.swal2-file::placeholder,.swal2-input::placeholder,.swal2-textarea::placeholder{color:#ccc}.swal2-range{margin:1em 2em 3px;background:#fff}.swal2-range input{width:80%}.swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}.swal2-range input,.swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}.swal2-input{height:2.625em;padding:0 .75em}.swal2-file{width:75%;margin-right:auto;margin-left:auto;background:inherit;font-size:1.125em}.swal2-textarea{height:6.75em;padding:.75em}.swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:inherit;color:inherit;font-size:1.125em}.swal2-checkbox,.swal2-radio{align-items:center;justify-content:center;background:#fff;color:inherit}.swal2-checkbox label,.swal2-radio label{margin:0 .6em;font-size:1.125em}.swal2-checkbox input,.swal2-radio input{flex-shrink:0;margin:0 .4em}.swal2-input-label{display:flex;justify-content:center;margin:1em auto 0}.swal2-validation-message{align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:#f0f0f0;color:#666;font-size:1em;font-weight:300}.swal2-validation-message::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}.swal2-icon{position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;border:.25em solid transparent;border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}.swal2-icon.swal2-error{border-color:#f27474;color:#f27474}.swal2-icon.swal2-error .swal2-x-mark{position:relative;flex-grow:1}.swal2-icon.swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}.swal2-icon.swal2-error.swal2-icon-show{-webkit-animation:swal2-animate-error-icon .5s;animation:swal2-animate-error-icon .5s}.swal2-icon.swal2-error.swal2-icon-show .swal2-x-mark{-webkit-animation:swal2-animate-error-x-mark .5s;animation:swal2-animate-error-x-mark .5s}.swal2-icon.swal2-warning{border-color:#facea8;color:#f8bb86}.swal2-icon.swal2-warning.swal2-icon-show{-webkit-animation:swal2-animate-error-icon .5s;animation:swal2-animate-error-icon .5s}.swal2-icon.swal2-warning.swal2-icon-show .swal2-icon-content{-webkit-animation:swal2-animate-i-mark .5s;animation:swal2-animate-i-mark .5s}.swal2-icon.swal2-info{border-color:#9de0f6;color:#3fc3ee}.swal2-icon.swal2-info.swal2-icon-show{-webkit-animation:swal2-animate-error-icon .5s;animation:swal2-animate-error-icon .5s}.swal2-icon.swal2-info.swal2-icon-show .swal2-icon-content{-webkit-animation:swal2-animate-i-mark .8s;animation:swal2-animate-i-mark .8s}.swal2-icon.swal2-question{border-color:#c9dae1;color:#87adbd}.swal2-icon.swal2-question.swal2-icon-show{-webkit-animation:swal2-animate-error-icon .5s;animation:swal2-animate-error-icon .5s}.swal2-icon.swal2-question.swal2-icon-show .swal2-icon-content{-webkit-animation:swal2-animate-question-mark .8s;animation:swal2-animate-question-mark .8s}.swal2-icon.swal2-success{border-color:#a5dc86;color:#a5dc86}.swal2-icon.swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;transform:rotate(45deg);border-radius:50%}.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=left]{top:-.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=right]{top:-.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}.swal2-icon.swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-.25em;left:-.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}.swal2-icon.swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}.swal2-icon.swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}.swal2-icon.swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}.swal2-icon.swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-tip{-webkit-animation:swal2-animate-success-line-tip .75s;animation:swal2-animate-success-line-tip .75s}.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-long{-webkit-animation:swal2-animate-success-line-long .75s;animation:swal2-animate-success-line-long .75s}.swal2-icon.swal2-success.swal2-icon-show .swal2-success-circular-line-right{-webkit-animation:swal2-rotate-success-circular-line 4.25s ease-in;animation:swal2-rotate-success-circular-line 4.25s ease-in}.swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:inherit;font-weight:600}.swal2-progress-steps li{display:inline-block;position:relative}.swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:#add8e6;color:#fff}.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:#add8e6}.swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}[class^=swal2]{-webkit-tap-highlight-color:transparent}.swal2-show{-webkit-animation:swal2-show .3s;animation:swal2-show .3s}.swal2-hide{-webkit-animation:swal2-hide .15s forwards;animation:swal2-hide .15s forwards}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}@-webkit-keyframes swal2-toast-show{0%{transform:translateY(-.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0)}}@keyframes swal2-toast-show{0%{transform:translateY(-.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0)}}@-webkit-keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@-webkit-keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@-webkit-keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}@-webkit-keyframes swal2-show{0%{transform:scale(.7)}45%{transform:scale(1.05)}80%{transform:scale(.95)}100%{transform:scale(1)}}@keyframes swal2-show{0%{transform:scale(.7)}45%{transform:scale(1.05)}80%{transform:scale(.95)}100%{transform:scale(1)}}@-webkit-keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(.5);opacity:0}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(.5);opacity:0}}@-webkit-keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@-webkit-keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@-webkit-keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@-webkit-keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(.4);opacity:0}50%{margin-top:1.625em;transform:scale(.4);opacity:0}80%{margin-top:-.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(.4);opacity:0}50%{margin-top:1.625em;transform:scale(.4);opacity:0}80%{margin-top:-.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@-webkit-keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0);opacity:1}}@-webkit-keyframes swal2-rotate-loading{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}@keyframes swal2-rotate-loading{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}@-webkit-keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@-webkit-keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto!important}body.swal2-no-backdrop .swal2-container{background-color:transparent!important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px rgba(0,0,0,.4)}@media print{body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow-y:scroll!important}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container{position:static!important}}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:transparent;pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{top:0;right:auto;bottom:auto;left:50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{top:0;right:0;bottom:auto;left:auto}body.swal2-toast-shown .swal2-container.swal2-top-left,body.swal2-toast-shown .swal2-container.swal2-top-start{top:0;right:auto;bottom:auto;left:0}body.swal2-toast-shown .swal2-container.swal2-center-left,body.swal2-toast-shown .swal2-container.swal2-center-start{top:50%;right:auto;bottom:auto;left:0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{top:50%;right:auto;bottom:auto;left:50%;transform:translate(-50%,-50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{top:50%;right:0;bottom:auto;left:auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-left,body.swal2-toast-shown .swal2-container.swal2-bottom-start{top:auto;right:auto;bottom:0;left:0}body.swal2-toast-shown .swal2-container.swal2-bottom{top:auto;right:auto;bottom:0;left:50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{top:auto;right:0;bottom:0;left:auto}</style></head>
<body>
    <header>
        <!--navbar start-->
        <nav class="navbar navbar-expand-lg navbar-light bg-light">
            <div class="container-fluid">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarText" aria-controls="navbarText" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse justify-content-between" id="navbarText">
                    <div>
                            <h1 class="btn btn-success mt-2">Welcome DUBI2</h1>
                            <a class="btn  btn-outline-danger" href="https://itpec.ur.gov.iq/Logout">Logout</a>
                            <a class="btn  btn-outline-danger" href="https://itpec.ur.gov.iq/Offices/Offices/Details">بياناتي</a>
                    </div>
                    <div>

                        <ul class="navbar-nav ms-auto align-items-center">





                                <li class="nav-item mx-0 mx-lg-1">
                                    <div class="dropdown">
                                        <a class="btn btn-secondary dropdown-toggle" href="https://itpec.ur.gov.iq/Offices/Contracts/Create#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            مكاتب العقود
                                        </a>
                                        <ul class="dropdown-menu">

                                            <li><a class="dropdown-item" href="https://itpec.ur.gov.iq/Offices/Contracts">ابرام العقود</a></li>

                                        </ul>
                                    </div>
                                </li>




                        </ul>
                    </div>
                    <div>
                        <a class="text-muted h3 mx-2" href="https://itpec.ur.gov.iq/">نظام العقد الالكتروني</a>
                    </div>
                </div>
            </div>
        </nav>
        <!--navbar end-->
    </header>
    <div>
        <div class="container">
            

<h1>ابرام عقد جديد</h1>

<hr>

<ul class="nav nav-tabs" id="myTab" hidden="" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="vendor-tab" data-bs-toggle="tab" data-bs-target="#vendor" type="button" role="tab" aria-controls="vendor" aria-selected="false">بيانات البائع</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="car-tab" data-bs-toggle="tab" data-bs-target="#car" type="button" role="tab" aria-controls="car" aria-selected="true">بيانات المركبة</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="client-tab" data-bs-toggle="tab" data-bs-target="#client" type="button" role="tab" aria-controls="client" aria-selected="false">بيانات المشتري</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="submit-tab" data-bs-toggle="tab" data-bs-target="#submit" type="button" role="tab" aria-controls="submit" aria-selected="false">البيانات الاخرى</button>
    </li>
</ul>

<form enctype="multipart/form-data" action="https://itpec.ur.gov.iq/Offices/Contracts/Create" method="post" novalidate="novalidate">
    <div class="tab-content" id="myTabContent">
        

        <div class="tab-pane fade" id="vendor" role="tabpanel" aria-labelledby="vendor-tab">
            <div class="row">
                <h2>معلومات البائع</h2>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label" for="Vendor_NameAr">اسم البائع</label>
                        <input class="form-control valid" type="text" data-val="true" data-val-required="يرجي مليء حقل اسم البائع" id="Vendor_NameAr" name="Vendor.NameAr" value="" autocomplete="off" aria-describedby="Vendor_NameAr-error" aria-invalid="false">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Vendor.NameAr" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Vendor_Identity">رقم الهوية</label>
                        <input class="form-control valid" type="text" data-val="true" data-val-length="رقم الهوية يجب ان يكون بين 12 و 2 رقم" data-val-length-max="12" data-val-length-min="2" data-val-required="يرجي مليء حقل رقم البطاقة الوطنية" id="Vendor_Identity" name="Vendor.Identity" value="" autocomplete="off" aria-describedby="Vendor_Identity-error" aria-invalid="false">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Vendor.Identity" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Vendor_Issuer">جهة الاصدار</label>
                        <input class="form-control valid" type="text" data-val="true" data-val-required="يرجي مليء حقل جهة اصدار البطاقة" id="Vendor_Issuer" name="Vendor.Issuer" value="" autocomplete="off" aria-describedby="Vendor_Issuer-error" aria-invalid="false">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Vendor.Issuer" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label" for="Vendor_PhoneNumber">رقم الهاتف</label>
                        <input class="form-control valid" type="text" data-val="true" data-val-length="رقم الهاتف يجب ان يكون 11 رقم" data-val-length-max="11" data-val-length-min="11" data-val-required="يرجي مليء حقل رقم الهاتف" id="Vendor_PhoneNumber" name="Vendor.PhoneNumber" value="" autocomplete="off" aria-describedby="Vendor_PhoneNumber-error" aria-invalid="false">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Vendor.PhoneNumber" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label cpas="" class="control-label" for="Vendor_Address_ProvinceId">المحافظة</label>
                        <select class="form-control valid" data-val="true" data-val-required="The ProvinceId field is required." id="Vendor_Address_ProvinceId" name="Vendor.Address.ProvinceId" aria-describedby="Vendor_Address_ProvinceId-error" aria-invalid="false"><option value="1">يرجى اختيار المحافظة</option>
<option value="2">البصرة</option>
<option value="3">نينوى</option>
<option value="4">بابل</option>
<option value="5">واسط</option>
<option value="6">كربلاء المقدسة</option>
<option value="7">النجف الاشرف</option>
<option value="8">القادسية</option>
<option value="9">المثنى</option>
<option value="10">ذي قار</option>
<option value="11">ميسان</option>
<option value="12">ديالى</option>
<option value="13">صلاح الدين</option>
<option value="14">كركوك</option>
<option value="15">الانبار</option>
<option value="16">بغداد</option>
<option value="17">سليمانية</option>
<option value="18">اربيل</option>
<option value="19">دهوك</option>
<option value="20">اخرى</option>
</select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Vendor.Address.ProvinceId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Vendor_Address_CityId">المدينة</label>
                        <select class="form-control valid" data-val="true" data-val-required="The CityId field is required." id="Vendor_Address_CityId" name="Vendor.Address.CityId" aria-describedby="Vendor_Address_CityId-error" aria-invalid="false"><option value="0" disabled="" selected=""> يرجى اختيار المدينة </option> <option value="168">النجف</option> <option value="169">الكوفة</option> <option value="170">المناذره</option> <option value="171">المشخاب</option> <option value="172">الحيدرية</option> <option value="173">الحرية</option> <option value="174">العباسية</option> <option value="175">النور</option> <option value="176">الشبكة</option> <option value="177">الرضوية</option> <option value="178">الحيرة</option> <option value="179">القادسية</option> <option value="180">اخرى</option></select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Vendor.Address.CityId" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label" for="Vendor_Address_LocationId">المحلة</label>
                        <input class="form-control valid" type="text" data-val="true" data-val-length="رقم المحلة يجب ان يكون من 1 الي 8 حرف" data-val-length-max="8" data-val-length-min="1" data-val-required="يرجي ادخال المحلة" id="Vendor_Address_LocationId" name="Vendor.Address.LocationId" value="" autocomplete="off" aria-describedby="Vendor_Address_LocationId-error" aria-invalid="false">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Vendor.Address.LocationId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Vendor_Address_ZokakId">رقم الزقاق</label>
                        <input class="form-control valid" type="text" data-val="true" data-val-length="رقم الزقاق يجب ان يكون من 1 الي 8 حرف" data-val-length-max="8" data-val-length-min="1" data-val-required="يرجي ادخال الزقاق" id="Vendor_Address_ZokakId" name="Vendor.Address.ZokakId" value="" autocomplete="off" aria-describedby="Vendor_Address_ZokakId-error" aria-invalid="false">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Vendor.Address.ZokakId" data-valmsg-replace="true"></span>
                    </div>

                    <div class="form-group">
                        <label class="control-label" for="Vendor_Address_HomeId">رقم الدار</label>
                        <input class="form-control valid" type="text" data-val="true" data-val-length="رقم الدار يجب ان يكون من 1 الي 8 حرف" data-val-length-max="8" data-val-length-min="1" data-val-required="يرجي ادخال رقم الدار" id="Vendor_Address_HomeId" name="Vendor.Address.HomeId" value="" autocomplete="off" aria-describedby="Vendor_Address_HomeId-error" aria-invalid="false">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Vendor.Address.HomeId" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div>
                    <input type="submit" id="goToCar" value="الذهاب الي بيانات المركبة" class="btn btn-outline-info mt-2 checkingValues" autocomplete="off">
                    <a class="mt-2 btn btn-outline-primary" href="https://itpec.ur.gov.iq/Offices/Contracts">الرجوع الي الصفحة الرئيسية</a>
                </div>
            </div>
        </div>

        <div class="tab-pane fade active show" id="car" role="tabpanel" aria-labelledby="car-tab">
            <div class="row">
                <h2>معلومات المركبة</h2>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label" for="Car_PlateId">رقم اللوحة</label>
                        <input class="form-control valid" type="text" data-val="true" data-val-required="يرجى مليء حقل رقم المركبة" id="Car_PlateId" name="Car.PlateId" value="" autocomplete="off" aria-describedby="Car_PlateId-error" aria-invalid="false">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.PlateId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Car_PlateSymId">رمز اللوحة</label>
                        <select class="form-control valid" data-val="true" data-val-required="يرجى مليء حقل رمز المركبة" id="Car_PlateSymId" name="Car.PlateSymId" aria-describedby="Car_PlateSymId-error" aria-invalid="false"><option value="1">أ-A</option>
<option value="2">ب-B</option>
<option value="3">ج-J</option>
<option value="4">د-D</option>
<option value="5">هـ-H</option>
<option value="6">ي-E</option>
<option value="7">ف-F</option>
<option value="8">س-S</option>
<option value="9">ر-R</option>
<option value="10">ن-N</option>
<option value="11">م-M</option>
<option value="12">و-W</option>
<option value="13">ك-K</option>
<option value="14">ق-Q</option>
<option value="15">ل-L</option>
<option value="16">ز-Z</option>
<option value="17">ط -T</option>
<option value="18">بلا</option>
</select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.PlateSymId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Car_ProvinceId"> محل التسجيل المركبة</label>
                        <select class="form-control valid" data-val="true" data-val-required="يرجى مليء حقل محل تسجيل المركبة" id="Car_ProvinceId" name="Car.ProvinceId" aria-describedby="Car_ProvinceId-error" aria-invalid="false"><option value="1">يرجى اختيار المحافظة</option>
<option value="2">البصرة</option>
<option value="3">نينوى</option>
<option value="4">بابل</option>
<option value="5">واسط</option>
<option value="6">كربلاء المقدسة</option>
<option value="7">النجف الاشرف</option>
<option value="8">القادسية</option>
<option value="9">المثنى</option>
<option value="10">ذي قار</option>
<option value="11">ميسان</option>
<option value="12">ديالى</option>
<option value="13">صلاح الدين</option>
<option value="14">كركوك</option>
<option value="15">الانبار</option>
<option value="16">بغداد</option>
<option value="17">سليمانية</option>
<option value="18">اربيل</option>
<option value="19">دهوك</option>
<option value="20">اخرى</option>
</select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.ProvinceId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Car_LincesNum">رقم السنوية</label>
                        <input class="form-control" type="text" data-val="true" data-val-required="يرجى مليء حقل بطاقة سنوية المركبة" id="Car_LincesNum" name="Car.LincesNum" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.LincesNum" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label" for="Car_YearValue">سنةالصنع</label>
                        <input class="form-control" type="text" data-val="true" data-val-required="يرجى مليء حقل سنة الصنع" id="Car_YearValue" name="Car.YearValue" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.YearValue" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Car_CategoryId">صنف المركبة</label>
                        <select class="form-control" data-val="true" data-val-required="يرجى مليء حقل صنف المركبة" id="Car_CategoryId" name="Car.CategoryId"><option value="1">خصوصي</option>
<option value="2">اجره</option>
<option value="3">حمل</option>
<option value="4">انشائية</option>
<option value="5">زراعية</option>
<option value="6">فحص مؤقت</option>
<option value="7">دراجه نارية</option>
<option value="8">حكومية</option>
<option value="9">حكومي</option>
<option value="10">اخرى</option>
</select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.CategoryId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Car_VIN">رقم الشاصي</label>
                        <input class="form-control" type="text" data-val="true" data-val-length="الشاصي يجب ان يكون بين 17 و 3 رقم" data-val-length-max="17" data-val-length-min="3" data-val-required="يرجى مليء حقل الشاصي" id="Car_VIN" name="Car.VIN" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.VIN" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label" for="Car_ModelId">موديل المركبة</label>
                        <select class="form-control" data-val="true" data-val-required="يرجى مليء حقول تفاصيل موديل المركبة" id="Car_ModelId" name="Car.ModelId"><option value="1">تويوتا</option>
<option value="2">نيسان</option>
<option value="3">كيا</option>
<option value="4">هونداي</option>
<option value="5">دوج</option>
<option value="6">شوفيرليت</option>
<option value="7">مارسيدس</option>
<option value="8">بي ام دبليو</option>
<option value="9">هوندا</option>
<option value="10">اوبل</option>
<option value="11">اودي</option>
<option value="12">بينتلي</option>
<option value="13">كريسلر</option>
<option value="14">جيب</option>
<option value="15">همر</option>
<option value="16">لكزس</option>
<option value="17">سايبا</option>
<option value="18">بيجو</option>
<option value="19">شيري</option>
<option value="20">BYD</option>
<option value="21">سمند</option>
<option value="22">انفنيتي</option>
<option value="23">جينسس</option>
<option value="24">شان جان</option>
<option value="25">لاندروفر</option>
<option value="26">فورد</option>
<option value="27">كاديلاك</option>
<option value="28">بيوك</option>
<option value="29">جي ام سي</option>
<option value="30">فولكس واكن</option>
<option value="31">فولفو</option>
<option value="32">بورش</option>
<option value="33">هينو</option>
<option value="34">ايسوزو</option>
<option value="35">تايه</option>
<option value="36">مازدا</option>
<option value="37">ميركوري</option>
<option value="38">ميستوبيشي</option>
<option value="39">رام</option>
<option value="40">سوزوكي</option>
<option value="41">دراجه</option>
<option value="42">اسوزو</option>
<option value="43">ليفان</option>
<option value="44">بابل</option>
<option value="45">اوباما</option>
<option value="46">اولدز موبيل</option>
<option value="47">بروتون</option>
<option value="48">دايو</option>
<option value="49">رينو</option>
<option value="50">جنبي</option>
<option value="51">أكد</option>
<option value="52">رانج روفر</option>
<option value="53">سامسونج</option>
<option value="54">سانك يانك</option>
<option value="55">سكانيا</option>
<option value="56">سكودا</option>
<option value="57">فيات</option>
<option value="58">افيكو</option>
<option value="59">بيوك</option>
<option value="60">داهيتسو</option>
<option value="61">سوبارو</option>
<option value="62">جاكور</option>
<option value="63">تاتا</option>
<option value="64">كريت وول</option>
<option value="65">فوتون</option>
<option value="66">كاوساكي</option>
<option value="67">كتربلر</option>
<option value="68">ادميرال</option>
<option value="69">دير</option>
<option value="70">سانغ يونغ</option>
<option value="71">طيبه</option>
<option value="72">تشانجان</option>
<option value="73">باجاج</option>
<option value="74">جيلي</option>
<option value="75">ام جي</option>
<option value="76">مان</option>
<option value="77">فوتون</option>
<option value="78">باو</option>
<option value="79">الوان فوتو</option>
<option value="80">فاو</option>
<option value="81">فولكا</option>
<option value="82">ناونيشان</option>
<option value="83">كوناو</option>
<option value="84">واز</option>
<option value="85">اخرى</option>
<option value="86">بربليانس</option>
<option value="87">XCMG</option>
<option value="88">XCMG</option>
<option value="89">XCMG</option>
<option value="90">دونج فنج</option>
<option value="91">دونج فينج</option>
<option value="92">ينمار</option>
<option value="93">جوي</option>
<option value="94">جمبي</option>
<option value="95">جيتور</option>
<option value="96">صيني</option>
</select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.ModelId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Car_TypeId">نوع المركبة</label>
                        <select disabled="" class="form-control" data-val="true" data-val-required="يرجى مليء حقل نوع المركبة" id="Car_TypeId" name="Car.TypeId"></select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.TypeId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Car_ColorsId">لون المركبة</label>
                        <select class="form-control" data-val="true" data-val-required="يرجى مليء حقل لون المركبة" id="Car_ColorsId" name="Car.ColorsId"><option value="1">ابيض غير محدد</option>
<option value="2">ابيض لولؤي</option>
<option value="3">احمر غير محدد</option>
<option value="4">برتقالي</option>
<option value="5">كرزي</option>
<option value="6">اصفر غير محدد</option>
<option value="7">اصفر برتقالي</option>
<option value="8">ذهبي</option>
<option value="9">شمباني</option>
<option value="10">اسود</option>
<option value="11">اسود كحلي</option>
<option value="12">رصاصي</option>
<option value="13">فيلي</option>
<option value="14">اخضر</option>
<option value="15">رمادي</option>
<option value="16">فضي</option>
<option value="17">ازرق غير محدد</option>
<option value="18">ازرق بحري </option>
<option value="19">ابيض حليبي</option>
<option value="20">ابيض شكري</option>
<option value="21">ابيض صدفي</option>
<option value="22">ابيض ملون</option>
<option value="23">ابيض مصفر</option>
<option value="24">اصفر تبني</option>
<option value="25">اصفر بيجي</option>
<option value="26">اصفر كريمي</option>
<option value="27">اصفر خمري</option>
<option value="28">اصفر ليموني</option>
<option value="29">اصفر خاكي</option>
<option value="30">اصفر ذهبي</option>
<option value="31">اصفر خردلي</option>
<option value="32">اصفر عسلي</option>
<option value="33">اصفر شمباني</option>
<option value="34">اصفر غير محدد</option>
<option value="35">احمر غير محدد</option>
<option value="36">احمر ماروني</option>
<option value="37">احمر وردي</option>
<option value="38">احمر عنابي</option>
<option value="39">احمر قرميدي</option>
<option value="40">احمر كرزي</option>
<option value="41">احمر خمري</option>
<option value="42">احمر ملون</option>
<option value="43">اخضر غير محدد</option>
<option value="44">اخضر حشيشي</option>
<option value="45">اخضر فستقي</option>
<option value="46">اخضر زيتوني</option>
<option value="47">اخضر ملون</option>
<option value="48">اسود غير محدد</option>
<option value="49">اسود فحمي</option>
<option value="50">اسود كحلي</option>
<option value="51">اسود باذنجاني</option>
<option value="52">اسود نفطي</option>
<option value="53">اسود ملون</option>
<option value="54">جوزي غير محدد</option>
<option value="55">جوزي حني</option>
<option value="56">جوزي برونزي</option>
<option value="57">جوزي قهوائي</option>
<option value="58">جوزي بني</option>
<option value="59">جوزي برونزي</option>
<option value="60">جوزي ككوي</option>
<option value="61">جوزي ملون</option>
<option value="62">برتقالي غير محدد</option>
<option value="63">برتقالي بصلي</option>
<option value="64">برتقالي مصفر</option>
<option value="65">برتقالي مشمشي</option>
<option value="66">برتقالي ملون</option>
<option value="67">رصاصي غير محدد</option>
<option value="68">رصاصي فضي</option>
<option value="69">رصاصي رمادي</option>
<option value="70">رصاصي فيراني</option>
<option value="71">رصاصي دخاني</option>
<option value="72">رصاصي ملون</option>
<option value="73">ازرق غير محدد</option>
<option value="74">ازرق سمائي</option>
<option value="75">ازرق نيلي</option>
<option value="76">ازرق شذري</option>
<option value="77">ازرق ماوي</option>
<option value="78">ازرق تركوازي</option>
<option value="79">ازرق فيروزي</option>
<option value="80">ازرق ملون</option>
<option value="81">بنفسجي</option>
<option value="82">بنفسجي ملون</option>
<option value="83">بنفسجي غير محدد</option>
<option value="84">مشمشي غير محدد</option>
<option value="85">مشمشي ملون</option>
<option value="86">ابيض</option>
<option value="87">اخرى</option>
<option value="88">اصفر شمباني</option>
<option value="89">احمر كرزي</option>
<option value="90">احمر</option>
<option value="91">رصاصي غامق</option>
<option value="92">نفطي</option>
<option value="94">رمادي غير محدد</option>
</select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Car.ColorsId" data-valmsg-replace="true"></span>
                    </div>
                </div>
            </div>
            <div>
                <input type="submit" id="goToClient" value="الذهاب الي بيانات المشتري" class="mt-2 btn btn-outline-info checkingValues" autocomplete="off">
                <input id="BackToVendor" value="الذهاب الي بيانات البائع" class="btn btn-outline-primary mt-2" autocomplete="off">
            </div>
        </div>

        <div class="tab-pane fade" id="client" role="tabpanel" aria-labelledby="client-tab">
            <div class="row">
                <h2>معلومات المشتري</h2>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label" for="Client_NameAr">اسم المشتري</label>
                        <input class="form-control" type="text" data-val="true" data-val-required="يرجي مليء حقل اسم المشتري" id="Client_NameAr" name="Client.NameAr" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Client.NameAr" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Client_Identity">رقم الهوية</label>
                        <input class="form-control" type="text" data-val="true" data-val-length="رقم الهوية يجب ان يكون بين 12 و 2 رقم" data-val-length-max="12" data-val-length-min="2" data-val-required="يرجي مليء حقل رقم البطاقة الوطنية" id="Client_Identity" name="Client.Identity" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Client.Identity" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Client_Issuer">جهة الاصدار</label>
                        <input class="form-control" type="text" data-val="true" data-val-required="يرجي مليء جهة اصدار البطاقة" id="Client_Issuer" name="Client.Issuer" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Client.Issuer" data-valmsg-replace="true"></span>
                    </div>

                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label cpas="" class="control-label" for="Client_Address_ProvinceId">المحافظة</label>
                        <select class="form-control" data-val="true" data-val-required="The ProvinceId field is required." id="Client_Address_ProvinceId" name="Client.Address.ProvinceId"><option value="1">يرجى اختيار المحافظة</option>
<option value="2">البصرة</option>
<option value="3">نينوى</option>
<option value="4">بابل</option>
<option value="5">واسط</option>
<option value="6">كربلاء المقدسة</option>
<option value="7">النجف الاشرف</option>
<option value="8">القادسية</option>
<option value="9">المثنى</option>
<option value="10">ذي قار</option>
<option value="11">ميسان</option>
<option value="12">ديالى</option>
<option value="13">صلاح الدين</option>
<option value="14">كركوك</option>
<option value="15">الانبار</option>
<option value="16">بغداد</option>
<option value="17">سليمانية</option>
<option value="18">اربيل</option>
<option value="19">دهوك</option>
<option value="20">اخرى</option>
</select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Client.Address.ProvinceId" data-valmsg-replace="true"></span>
                    </div>

                    <div class="form-group">
                        <label class="control-label" for="Client_Address_CityId">المدينة</label>
                        <select disabled="" class="form-control" data-val="true" data-val-required="The CityId field is required." id="Client_Address_CityId" name="Client.Address.CityId"></select>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Client.Address.CityId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Client_PhoneNumber">رقم الهاتف</label>
                        <input class="form-control" type="text" data-val="true" data-val-length="رقم الهاتف يجب ان يكون 11 رقم" data-val-length-max="11" data-val-length-min="11" data-val-required="يرجي مليء حقل رقم الهاتف" id="Client_PhoneNumber" name="Client.PhoneNumber" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Client.PhoneNumber" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label" for="Client_Address_LocationId">المحلة</label>
                        <input class="form-control" type="text" data-val="true" data-val-length="رقم المحلة يجب ان يكون من 1 الي 8 حرف" data-val-length-max="8" data-val-length-min="1" data-val-required="يرجي ادخال المحلة" id="Client_Address_LocationId" name="Client.Address.LocationId" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Client.Address.LocationId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Client_Address_ZokakId">رقم الزقاق</label>
                        <input class="form-control" type="text" data-val="true" data-val-length="رقم الزقاق يجب ان يكون من 1 الي 8 حرف" data-val-length-max="8" data-val-length-min="1" data-val-required="يرجي ادخال الزقاق" id="Client_Address_ZokakId" name="Client.Address.ZokakId" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Client.Address.ZokakId" data-valmsg-replace="true"></span>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="Client_Address_HomeId">رقم الدار</label>
                        <input class="form-control" type="text" data-val="true" data-val-length="رقم الدار يجب ان يكون من 1 الي 8 حرف" data-val-length-max="8" data-val-length-min="1" data-val-required="يرجي ادخال رقم الدار" id="Client_Address_HomeId" name="Client.Address.HomeId" value="" autocomplete="off">
                        <span class="text-danger field-validation-valid" data-valmsg-for="Client.Address.HomeId" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div>
                    <input type="submit" id="goToSubmit" value="الذهاب الي انهاء العقد" class="btn btn-outline-info mt-2 checkingValues" autocomplete="off">
                    <input id="BackToCar" value="الذهاب الي بيانات المركبة" class="btn btn-outline-primary mt-2" autocomplete="off">
                </div>
            </div>
        </div>

        <div class="tab-pane fade" id="submit" role="tabpanel" aria-labelledby="submit-tab">
            <div class="row">
                <h2>مبلغ بيع المركبة</h2>
                <div class="form-group">
                    <label class="control-label" for="Contract_TotalAmount">مبلغ المركبة</label>
                    <input value="0.00" class="form-control" type="text" data-val="true" data-val-number="The field TotalAmount must be a number." data-val-required="The TotalAmount field is required." id="Contract_TotalAmount" name="Contract.TotalAmount" autocomplete="off">
                    <span class="text-danger field-validation-valid" data-valmsg-for="Contract.TotalAmount" data-valmsg-replace="true"></span>
                </div>
                <div class="form-group">
                    <label class="control-label" for="Contract_Paid">الواصل</label>
                    <input value="0.00" class="form-control" type="text" data-val="true" data-val-number="The field Paid must be a number." data-val-required="The Paid field is required." id="Contract_Paid" name="Contract.Paid" autocomplete="off">
                    <span class="text-danger field-validation-valid" data-valmsg-for="Contract.Paid" data-valmsg-replace="true"></span>
                </div>
                <div class="form-group">
                    <label class="control-label" for="Contract_Reminaing">الباقي</label>
                    <input value="0.00" class="form-control disabled" type="text" data-val="true" data-val-number="The field Reminaing must be a number." data-val-required="The Reminaing field is required." id="Contract_Reminaing" name="Contract.Reminaing" autocomplete="off">
                    <span class="text-danger field-validation-valid" data-valmsg-for="Contract.Reminaing" data-valmsg-replace="true"></span>
                </div>
                <div class="form-group">
                    <label class="control-label" for="Contract_Note">الملاحظات</label>
                    <input class="form-control" type="text" id="Contract_Note" name="Contract.Note" value="" autocomplete="off">
                    <span class="text-danger field-validation-valid" data-valmsg-for="Contract.Note" data-valmsg-replace="true"></span>
                </div>
            </div>
            <div class="form-group mt-2">
                <input type="submit" id="submitBtn" value="ابرام العقد" class="btn btn-outline-info mt-2" autocomplete="off">
                <input id="BackToClient" value="الذهاب الي بيانات المشتري" class="btn btn-outline-primary mt-2" autocomplete="off">
            </div>
        </div>
    </div>
<input name="__RequestVerificationToken" type="hidden" value="CfDJ8DJfuYIIQbRLhxvVppxGdymE6s3IzypYG-GwmosuQic2ug8voXsY9amahiEAEsw3qXjE79knAlPeNRpHsmXEmiJBI1VyhgOKgCmmHDCOBZ6vSkxmqpWnbalZjpwPdLvzMCZ1xjatE86o8gjNkgMrS-USVr-4xKmkFHYvNI7Lsm_GV5kzBX3atM6sFN3W8FzJZA" autocomplete="off"></form>


        </div>
    </div>
    <footer>
    </footer>

    <!--jquery-->
    <script src="./Create_files/jquery.js.تنزيل"></script>
    <!--DataTable-->
    <script src="./Create_files/jquery.datatables.min.js.تنزيل"></script>
    <script src="./Create_files/dataTables.buttons.min.js.تنزيل"></script>
    <script src="./Create_files/jszip.min.js.تنزيل"></script>
    <script src="./Create_files/pdfmake.min.js.تنزيل"></script>
    <script src="./Create_files/vfs_fonts.js.تنزيل"></script>
    <script src="./Create_files/buttons.html5.min.js.تنزيل"></script>
    <!--bootstrap-->
    <script src="./Create_files/bootstrap.bundle.min.js.تنزيل"></script>
    <!--select2-->
    <script src="./Create_files/select2.full.min.js.تنزيل"></script>
    <!--sweetalert-->
    <script src="./Create_files/sweetalert2.all.min.js.تنزيل"></script>
    <!--gijgo-->
    <script src="./Create_files/gijgo.min.js.تنزيل"></script>
    <!--siteJs-->
    <script src="./Create_files/daterangepicker.js.تنزيل"></script>
    <script src="./Create_files/global.min.obfuscated.js.تنزيل"></script>
    <script src="./Create_files/auto-execution.min.obfuscated.js.تنزيل"></script>
    <script src="./Create_files/layout.min.obfuscated.js.تنزيل"></script>

    
    <script src="./Create_files/jquery.validate.js.تنزيل"></script>
<script src="./Create_files/jquery.validate.unobtrusive.js.تنزيل"></script>

    <script src="./Create_files/Create.min.obfuscated.js.تنزيل"></script>



</body></html>