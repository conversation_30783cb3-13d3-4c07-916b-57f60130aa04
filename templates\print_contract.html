<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة العقد - رقم {{ contract.serial_number }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .main-content {
            padding: 20px;
        }
        
        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        
        .contract-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #007bff;
        }
        
        .info-section h3 {
            margin: 0 0 15px 0;
            color: #007bff;
            font-size: 18px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .photos-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .photo-container {
            text-align: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        
        .photo-container img {
            max-width: 150px;
            max-height: 200px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
        
        .photo-label {
            margin-top: 10px;
            font-weight: bold;
            color: #007bff;
        }

        .qr-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .qr-code {
            max-width: 120px !important;
            max-height: 120px !important;
            border: 1px solid #ddd !important;
        }
        
        .financial-section {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #007bff;
        }
        
        .financial-section h3 {
            color: #007bff;
            margin: 0 0 15px 0;
            text-align: center;
        }
        
        .amount-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        
        .amount-label {
            font-weight: bold;
            color: #495057;
        }
        
        .amount-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        
        .amount-text {
            font-style: italic;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
        }
        
        .btn {
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .print-container {
                box-shadow: none;
                border-radius: 0;
                padding: 20px;
            }
            
            .actions {
                display: none;
            }
        }
        
        .notes-section {
            margin: 20px 0;
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #ffc107;
        }
        
        .notes-section h3 {
            color: #856404;
            margin: 0 0 10px 0;
        }
        
        .note-item {
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    {% include 'toolbar.html' %}

    <div class="main-content">
        <div class="print-container">
        <div class="header">
            <h1>عقد بيع سيارة</h1>
            <p>رقم العقد: {{ contract.serial_number }}</p>
            <p>تاريخ الإبرام: {{ contract.created_at }}</p>
            {% if pdf_failed %}
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <strong>⚠️ تنبيه:</strong> لم يتم تحويل العقد إلى PDF. يتم عرض العقد في صفحة ويب للطباعة.
            </div>
            {% endif %}
        </div>
        
        <div class="contract-info">
            <div class="info-section">
                <h3>بيانات البائع</h3>
                <div class="info-row">
                    <span class="info-label">الاسم:</span>
                    <span class="info-value">{{ contract.name_2 or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ contract.location_2 or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهوية:</span>
                    <span class="info-value">{{ contract.id_1 or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value">{{ contract.phone_1 or 'غير محدد' }}</span>
                </div>
            </div>
            
            <div class="info-section">
                <h3>بيانات المشتري</h3>
                <div class="info-row">
                    <span class="info-label">الاسم:</span>
                    <span class="info-value">{{ contract.name_3 or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ contract.location_3 or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهوية:</span>
                    <span class="info-value">{{ contract.id_2 or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value">{{ contract.phone_2 or 'غير محدد' }}</span>
                </div>
            </div>
        </div>
        
        <div class="info-section">
            <h3>بيانات السيارة</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <div class="info-row">
                    <span class="info-label">نوع السيارة:</span>
                    <span class="info-value">{{ contract.car_type or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم السيارة:</span>
                    <span class="info-value">{{ contract.car_num or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">موديل السيارة:</span>
                    <span class="info-value">{{ contract.car_model or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">لون السيارة:</span>
                    <span class="info-value">{{ contract.car_colar or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الشاصي:</span>
                    <span class="info-value">{{ contract.sasi_num or 'غير محدد' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم المحرك:</span>
                    <span class="info-value">{{ contract.engen_num or 'غير محدد' }}</span>
                </div>
            </div>
        </div>
        
        <div class="financial-section">
            <h3>المبالغ المالية</h3>
            <div class="amount-row">
                <div>
                    <div class="amount-label">مبلغ البيع الكلي:</div>
                    <div class="amount-text">{{ contract.badal_writing or 'غير محدد' }}</div>
                </div>
                <div class="amount-value">{{ contract.badal_num or 'غير محدد' }}</div>
            </div>
            
            <div class="amount-row">
                <div>
                    <div class="amount-label">المبلغ المدفوع:</div>
                    <div class="amount-text">{{ contract.mony_writing or 'غير محدد' }}</div>
                </div>
                <div class="amount-value">{{ contract.mony_num or 'غير محدد' }}</div>
            </div>
            
            <div class="amount-row">
                <div>
                    <div class="amount-label">المبلغ المتبقي:</div>
                    <div class="amount-text">{{ contract.mony_not_delevired_writing or 'غير محدد' }}</div>
                </div>
                <div class="amount-value">{{ contract.mony_not_delevired or 'غير محدد' }}</div>
            </div>
        </div>
        
        {% if images.buyerPhoto or images.sellerPhoto or images.qrCode %}
        <div class="photos-section">
            {% if images.sellerPhoto %}
            <div class="photo-container">
                <img src="data:image/jpeg;base64,{{ images.sellerPhoto }}" alt="صورة البائع">
                <div class="photo-label">صورة البائع</div>
            </div>
            {% endif %}

            {% if images.buyerPhoto %}
            <div class="photo-container">
                <img src="data:image/jpeg;base64,{{ images.buyerPhoto }}" alt="صورة المشتري">
                <div class="photo-label">صورة المشتري</div>
            </div>
            {% endif %}

            {% if images.qrCode %}
            <div class="photo-container qr-container">
                <img src="data:image/png;base64,{{ images.qrCode }}" alt="رمز QR للعقد" class="qr-code">
                <div class="photo-label">رمز QR للعقد</div>
            </div>
            {% endif %}
        </div>
        {% endif %}
        
        {% if contract.note_a or contract.note_b %}
        <div class="notes-section">
            <h3>الملاحظات</h3>
            {% if contract.note_a %}
            <div class="note-item">
                <strong>ملاحظة أ:</strong> {{ contract.note_a }}
            </div>
            {% endif %}
            {% if contract.note_b %}
            <div class="note-item">
                <strong>ملاحظة ب:</strong> {{ contract.note_b }}
            </div>
            {% endif %}
        </div>
        {% endif %}
        
        <div class="actions">
            <button onclick="printContract()" class="btn btn-primary">🖨️ طباعة العقد</button>
            <button onclick="window.close()" class="btn btn-secondary">❌ إغلاق النافذة</button>
            <a href="/" class="btn btn-success">🔙 العودة للرئيسية</a>
        </div>
    </div>
    
    <script>
        // تحويل الصفحة إلى PDF عند الطباعة
        window.addEventListener('beforeprint', function() {
            document.title = `عقد_رقم_{{ contract.serial_number }}_{{ contract.name_2 or 'غير_محدد' }}`;
        });

        // فتح نافذة الطباعة تلقائياً عند تحميل الصفحة (بعد ثانيتين)
        window.addEventListener('load', function() {
            setTimeout(function() {
                // إظهار رسالة ترحيب
                const welcomeMsg = document.createElement('div');
                welcomeMsg.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    z-index: 1000;
                    font-size: 16px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                `;
                welcomeMsg.innerHTML = '✅ تم إبرام العقد بنجاح! يمكنك الآن طباعته.';
                document.body.appendChild(welcomeMsg);

                // إزالة الرسالة بعد 5 ثوان
                setTimeout(() => {
                    if (welcomeMsg.parentNode) {
                        welcomeMsg.parentNode.removeChild(welcomeMsg);
                    }
                }, 5000);
            }, 500);
        });

        // تحسين تجربة الطباعة
        function printContract() {
            // إخفاء الأزرار مؤقتاً
            const actions = document.querySelector('.actions');
            const originalDisplay = actions.style.display;
            actions.style.display = 'none';

            // طباعة
            window.print();

            // إعادة إظهار الأزرار
            setTimeout(() => {
                actions.style.display = originalDisplay;
            }, 1000);
        }
    </script>
        </div>
    </div>
</body>
</html>
