#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تنظيف الذاكرة والتحكم في Gradio Client
"""

import time
import threading
import psutil
import gc
from server import (
    update_request_activity,
    get_system_stats,
    should_cleanup_memory,
    perform_memory_cleanup,
    gradio_manager,
    start_memory_monitor,
    cleanup_gradio_specific_memory,
    cleanup_memory_after_request,
    force_memory_cleanup_if_needed,
    aggressive_memory_cleanup
)

def test_request_activity():
    """اختبار تتبع نشاط الطلبات"""
    print("🧪 اختبار تتبع نشاط الطلبات...")
    
    # محاكاة طلبات
    for i in range(5):
        update_request_activity()
        print(f"   طلب {i+1} تم تسجيله")
        time.sleep(0.5)
    
    print("✅ تم اختبار تتبع نشاط الطلبات بنجاح")

def test_system_stats():
    """اختبار الحصول على إحصائيات النظام"""
    print("🧪 اختبار إحصائيات النظام...")
    
    memory_mb, cpu_percent = get_system_stats()
    print(f"   الذاكرة: {memory_mb:.1f} MB")
    print(f"   المعالج: {cpu_percent:.1f}%")
    
    if memory_mb > 0:
        print("✅ تم اختبار إحصائيات النظام بنجاح")
    else:
        print("⚠️ قد تكون مكتبة psutil غير مثبتة")

def test_cleanup_conditions():
    """اختبار شروط التنظيف"""
    print("🧪 اختبار شروط التنظيف...")
    
    should_cleanup, memory_mb, cpu_percent = should_cleanup_memory()
    print(f"   هل يجب التنظيف: {should_cleanup}")
    print(f"   الذاكرة: {memory_mb:.1f} MB")
    print(f"   المعالج: {cpu_percent:.1f}%")
    
    print("✅ تم اختبار شروط التنظيف")

def test_gradio_manager():
    """اختبار مدير Gradio مع التنظيف الفوري للذاكرة"""
    print("🧪 اختبار مدير Gradio...")

    # اختبار الحصول على client
    client = gradio_manager.get_client()
    if client:
        print("   ✅ تم الحصول على Gradio Client")

        # فحص الذاكرة قبل التنظيف
        memory_before, _ = get_system_stats()
        print(f"   الذاكرة قبل التنظيف: {memory_before:.1f} MB")

        # اختبار التنظيف الفوري
        print("   اختبار التنظيف الفوري للذاكرة...")
        gradio_manager._cleanup_gradio_memory()

        # فحص الذاكرة بعد التنظيف الفوري
        time.sleep(2)
        memory_after_immediate, _ = get_system_stats()
        print(f"   الذاكرة بعد التنظيف الفوري: {memory_after_immediate:.1f} MB")

        # اختبار التنظيف التلقائي
        print("   انتظار 12 ثانية لاختبار التنظيف التلقائي...")
        time.sleep(12)

        # فحص ما إذا تم تنظيف Client
        with gradio_manager.lock:
            if gradio_manager.client is None:
                print("   ✅ تم تنظيف Gradio Client تلقائياً")
            else:
                print("   ⚠️ لم يتم تنظيف Gradio Client تلقائياً")

        # فحص الذاكرة بعد التنظيف التلقائي
        memory_final, _ = get_system_stats()
        print(f"   الذاكرة النهائية: {memory_final:.1f} MB")

    else:
        print("   ⚠️ فشل في الحصول على Gradio Client (قد تكون المكتبة غير مثبتة)")

def test_memory_cleanup():
    """اختبار تنظيف الذاكرة"""
    print("🧪 اختبار تنظيف الذاكرة...")
    
    # الحصول على إحصائيات قبل التنظيف
    memory_before, cpu_before = get_system_stats()
    print(f"   قبل التنظيف - الذاكرة: {memory_before:.1f} MB, المعالج: {cpu_before:.1f}%")
    
    # تنفيذ التنظيف
    success = perform_memory_cleanup()
    
    if success:
        # الحصول على إحصائيات بعد التنظيف
        time.sleep(2)  # انتظار قصير
        memory_after, cpu_after = get_system_stats()
        print(f"   بعد التنظيف - الذاكرة: {memory_after:.1f} MB, المعالج: {cpu_after:.1f}%")
        print("✅ تم اختبار تنظيف الذاكرة بنجاح")
    else:
        print("❌ فشل في تنظيف الذاكرة")

def simulate_high_memory_usage():
    """محاكاة استخدام عالي للذاكرة"""
    print("🧪 محاكاة استخدام عالي للذاكرة...")
    
    # إنشاء بيانات كبيرة في الذاكرة
    large_data = []
    for i in range(1000):
        large_data.append([0] * 10000)  # قوائم كبيرة
    
    memory_mb, cpu_percent = get_system_stats()
    print(f"   بعد إنشاء البيانات - الذاكرة: {memory_mb:.1f} MB")
    
    # تنظيف البيانات
    del large_data
    gc.collect()
    
    memory_mb, cpu_percent = get_system_stats()
    print(f"   بعد التنظيف - الذاكرة: {memory_mb:.1f} MB")
    
    print("✅ تم اختبار محاكاة استخدام الذاكرة")

def test_gradio_specific_cleanup():
    """اختبار تنظيف خاص بذاكرة Gradio"""
    print("🧪 اختبار تنظيف خاص بذاكرة Gradio...")

    # الحصول على إحصائيات قبل التنظيف
    memory_before, cpu_before = get_system_stats()
    print(f"   قبل التنظيف الخاص - الذاكرة: {memory_before:.1f} MB")

    # تنفيذ التنظيف الخاص بـ Gradio
    cleanup_gradio_specific_memory()

    # انتظار قصير
    time.sleep(2)

    # الحصول على إحصائيات بعد التنظيف
    memory_after, cpu_after = get_system_stats()
    print(f"   بعد التنظيف الخاص - الذاكرة: {memory_after:.1f} MB")

    print("✅ تم اختبار تنظيف خاص بذاكرة Gradio")

def test_request_memory_cleanup():
    """اختبار تنظيف الذاكرة بعد الطلبات"""
    print("🧪 اختبار تنظيف الذاكرة بعد الطلبات...")

    # الحصول على إحصائيات قبل التنظيف
    memory_before, cpu_before = get_system_stats()
    print(f"   قبل تنظيف الطلب - الذاكرة: {memory_before:.1f} MB")

    # محاكاة تنظيف بعد طلب
    cleanup_memory_after_request()

    # انتظار قصير
    time.sleep(2)

    # الحصول على إحصائيات بعد التنظيف
    memory_after, cpu_after = get_system_stats()
    print(f"   بعد تنظيف الطلب - الذاكرة: {memory_after:.1f} MB")

    print("✅ تم اختبار تنظيف الذاكرة بعد الطلبات")

def test_aggressive_cleanup():
    """اختبار التنظيف القوي للذاكرة"""
    print("🧪 اختبار التنظيف القوي للذاكرة...")

    # إنشاء بيانات كبيرة لمحاكاة استهلاك عالي
    large_data = []
    for i in range(2000):
        large_data.append([0] * 5000)

    memory_before, _ = get_system_stats()
    print(f"   قبل التنظيف القوي - الذاكرة: {memory_before:.1f} MB")

    # تنفيذ التنظيف القوي
    aggressive_memory_cleanup()

    # تنظيف البيانات المحلية
    del large_data

    # انتظار قصير
    time.sleep(3)

    memory_after, _ = get_system_stats()
    print(f"   بعد التنظيف القوي - الذاكرة: {memory_after:.1f} MB")

    print("✅ تم اختبار التنظيف القوي للذاكرة")

def test_memory_monitor():
    """اختبار مراقب الذاكرة"""
    print("🧪 اختبار مراقب الذاكرة...")

    # بدء مراقب الذاكرة
    start_memory_monitor()
    print("   تم بدء مراقب الذاكرة")

    # انتظار قصير
    time.sleep(5)

    print("✅ تم اختبار مراقب الذاكرة")

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار نظام تنظيف الذاكرة والتحكم في Gradio Client")
    print("=" * 60)
    
    try:
        test_request_activity()
        print()
        
        test_system_stats()
        print()
        
        test_cleanup_conditions()
        print()
        
        test_gradio_manager()
        print()
        
        test_memory_cleanup()
        print()
        
        simulate_high_memory_usage()
        print()

        test_gradio_specific_cleanup()
        print()

        test_request_memory_cleanup()
        print()

        test_aggressive_cleanup()
        print()

        test_memory_monitor()
        print()
        
        print("=" * 60)
        print("✅ تم إنجاز جميع الاختبارات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبارات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_tests()
