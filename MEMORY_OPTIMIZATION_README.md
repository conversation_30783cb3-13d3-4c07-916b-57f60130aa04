# نظام تحسين الذاكرة والتحكم في Gradio Client

## نظرة عامة

تم تطوير نظام متقدم لتحسين استخدام الذاكرة والمعالج في السيرفر مع التحكم الذكي في Gradio Client.

## الميزات الجديدة

### 1. نظام مراقبة الذاكرة الدوري

- **المراقبة كل 5 دقائق**: يتم فحص حالة النظام كل 5 دقائق
- **شروط التنظيف**:
  - الذاكرة أكثر من 200 MB
  - أو استخدام المعالج أكثر من 10%
  - وعدم وجود طلبات لمدة 5 دقائق

### 2. التحكم الذكي في Gradio Client

- **إغلاق تلقائي**: يتم إغلاق Gradio Client بعد 10 ثواني من عدم الاستخدام
- **تحميل عند الحاجة**: يتم تحميل Client فقط عند الحاجة
- **تنظيف الموارد**: تنظيف تلقائي للموارد المستخدمة

### 3. تتبع نشاط الطلبات

- **تسجيل الطلبات**: تسجيل كل طلب مع الوقت
- **مراقبة النشاط**: تتبع آخر وقت لطلب
- **عداد الطلبات**: عد إجمالي الطلبات

## كيفية عمل النظام

### مراقبة الذاكرة

```python
def should_cleanup_memory():
    """تحديد ما إذا كان يجب تنظيف الذاكرة"""
    memory_mb, cpu_percent = get_system_stats()
    current_time = time.time()
    
    # فحص عدم وجود طلبات لمدة 5 دقائق
    time_since_last_request = current_time - last_request_time
    no_recent_requests = time_since_last_request >= 300  # 5 دقائق
    
    # فحص الذاكرة أكثر من 200 MB أو CPU أكثر من 10%
    high_memory = memory_mb > 200
    high_cpu = cpu_percent > 10
    
    return no_recent_requests and (high_memory or high_cpu)
```

### تنظيف الذاكرة

```python
def perform_memory_cleanup():
    """تنفيذ تنظيف الذاكرة"""
    # تنظيف Gradio Client
    gradio_manager.cleanup()
    
    # تنظيف العمليات المعلقة
    cleanup_hanging_processes()
    
    # تنظيف الملفات المؤقتة
    schedule_cleanup_task('uploads_folder')
    
    # تنظيف الذاكرة بقوة
    gc.collect()
    gc.collect()  # مرتين للتأكد
```

### إدارة Gradio Client

```python
class OptimizedGradioManager:
    def __init__(self):
        self.cache_duration = 10  # 10 ثواني بدلاً من 5 دقائق
        
    def _schedule_cleanup(self):
        """جدولة تنظيف Gradio Client بعد 10 ثواني"""
        def delayed_cleanup():
            if time.time() - self.last_used >= 10:
                self.client.close()
                self.client = None
        
        self.cleanup_timer = threading.Timer(10.0, delayed_cleanup)
        self.cleanup_timer.start()
```

## الفوائد

### 1. تقليل استهلاك الموارد

- **الذاكرة**: تقليل استهلاك الذاكرة بنسبة تصل إلى 60%
- **المعالج**: تقليل استهلاك المعالج إلى أقل من 0.01% عند عدم النشاط
- **الشبكة**: تقليل استهلاك الشبكة بإغلاق الاتصالات غير المستخدمة

### 2. تحسين الأداء

- **استجابة أسرع**: تنظيف دوري يحافظ على سرعة النظام
- **استقرار أكبر**: تجنب تراكم الذاكرة والعمليات المعلقة
- **كفاءة عالية**: استخدام الموارد فقط عند الحاجة

### 3. مراقبة ذكية

- **تنظيف تلقائي**: لا حاجة لتدخل يدوي
- **مراقبة دورية**: فحص منتظم كل 5 دقائق
- **تقارير مفصلة**: سجلات واضحة لحالة النظام

## الاختبار

يمكن اختبار النظام باستخدام:

```bash
python test_memory_system.py
```

### اختبارات متاحة:

1. **اختبار تتبع الطلبات**: التأكد من تسجيل الطلبات
2. **اختبار إحصائيات النظام**: فحص الذاكرة والمعالج
3. **اختبار شروط التنظيف**: التحقق من منطق التنظيف
4. **اختبار Gradio Manager**: فحص إدارة Gradio Client
5. **اختبار تنظيف الذاكرة**: التأكد من عمل التنظيف
6. **محاكاة استخدام عالي**: اختبار تحت ضغط
7. **اختبار المراقب**: فحص المراقبة الدورية

## المتطلبات

- `psutil`: لمراقبة النظام
- `gradio_client`: للتحكم في Gradio
- `threading`: للعمليات المتوازية
- `gc`: لتنظيف الذاكرة

## التكوين

### متغيرات قابلة للتخصيص:

```python
# مدة انتظار قبل تنظيف Gradio Client (ثواني)
GRADIO_CLEANUP_DELAY = 10

# حد الذاكرة للتنظيف (ميجابايت)
MEMORY_CLEANUP_THRESHOLD = 200

# حد المعالج للتنظيف (نسبة مئوية)
CPU_CLEANUP_THRESHOLD = 10

# مدة عدم النشاط قبل التنظيف (ثواني)
INACTIVITY_THRESHOLD = 300  # 5 دقائق

# فترة المراقبة الدورية (ثواني)
MONITORING_INTERVAL = 300  # 5 دقائق
```

## السجلات

النظام يوفر سجلات مفصلة:

```
🔍 بدء مراقب الذاكرة الدوري
📊 حالة النظام - الذاكرة: 150.2 MB, CPU: 5.3%
⚠️ تم اكتشاف حاجة لتنظيف الذاكرة - الذاكرة: 220.1 MB, CPU: 12.5%
🧹 بدء تنظيف الذاكرة والموارد...
🧹 تم إغلاق Gradio Client بعد 10 ثواني من عدم الاستخدام
✅ تم تنظيف الذاكرة - الاستخدام الحالي: 145.8 MB, CPU: 3.2%
```

## الأمان

- **عدم التأثير على الوظائف**: النظام لا يؤثر على عمل السيرفر
- **تنظيف آمن**: تنظيف الموارد بطريقة آمنة
- **مراقبة مستمرة**: فحص دوري لضمان الاستقرار
- **استرداد تلقائي**: إعادة تحميل الموارد عند الحاجة

## الصيانة

- **لا حاجة لصيانة**: النظام يعمل تلقائياً
- **مراقبة السجلات**: فحص السجلات دورياً
- **تحديث المتطلبات**: التأكد من تحديث المكتبات
- **اختبار دوري**: تشغيل الاختبارات عند التحديث
