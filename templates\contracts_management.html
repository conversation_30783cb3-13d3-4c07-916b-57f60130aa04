<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العقود</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        .main-content {
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 35px;
            border-radius: 12px;
            box-shadow: 0 4px 25px rgba(0,0,0,0.08);
            border: 1px solid #e8e9ea;
        }
        
        .header {
            text-align: center;
            margin-bottom: 35px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 25px;
        }

        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 30px;
            font-weight: 700;
        }
        
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .controls-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .controls-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-group label {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-control {
            padding: 10px 14px;
            border: 1px solid #d1d9e0;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            outline: none;
        }

        #searchInput {
            position: relative;
        }

        #searchInput:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }


        
        .btn {
            padding: 10px 18px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background-color: #117a8b;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }
        
        .btn-warning {
            background-color: #17a2b8;
            color: white;
        }
        
        .btn-warning:hover {
            background-color: #117a8b;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
        
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            border-top: 2px solid #007bff;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .actions-cell {
            white-space: nowrap;
        }
        
        .actions-cell .btn {
            margin: 0 2px;
            padding: 6px 10px;
            font-size: 12px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        
        .pagination-info {
            color: #6c757d;
            font-size: 14px;
        }
        
        .no-contracts {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 18px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .controls-left,
            .controls-right {
                justify-content: center;
            }
            
            .table {
                font-size: 12px;
            }
            
            .table th,
            .table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <!-- شريط الأدوات العلوي -->
    {% include 'toolbar.html' %}

    <div class="main-content">
        <div class="container">
        <div class="header">
            <h1>إدارة العقود المبرمة</h1>
            <p>إجمالي العقود: {{ contracts|length }}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ contracts|length }}</div>
                <div class="stat-label">إجمالي العقود</div>
            </div>
            <div class="stat-card" style="background: linear-gradient(135deg, #28a745, #1e7e34);">
                <div class="stat-number">{{ contracts|length }}</div>
                <div class="stat-label">عقود هذا الشهر</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ contracts|length }}</div>
                <div class="stat-label">عقود اليوم</div>
            </div>
        </div>
        
        <div class="controls">
            <div class="controls-left">
                <div class="form-group">
                    <label for="recordsPerPage">عرض:</label>
                    <select id="recordsPerPage" class="form-control" onchange="changeRecordsPerPage()">
                        <option value="10">10</option>
                        <option value="25" selected>25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span>عقد في الصفحة</span>
                </div>
                
                <div class="form-group">
                    <label for="searchType">نوع البحث:</label>
                    <select id="searchType" class="form-control" onchange="searchContracts();">
                        <option value="all">🔍 البحث في كل شيء</option>
                        <option value="serial">🔢 رقم العقد</option>
                        <option value="seller">👤 اسم البائع</option>
                        <option value="buyer">🛒 اسم المشتري</option>
                        <option value="seller_phone">📞 هاتف البائع</option>
                        <option value="buyer_phone">📱 هاتف المشتري</option>
                        <option value="car_number">🚗 رقم السيارة</option>
                        <option value="car_type">🚙 نوع السيارة</option>
                        <option value="date">📅 تاريخ العقد</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="searchInput">البحث:</label>
                    <input type="text" id="searchInput" class="form-control" placeholder="ابحث في العقود..." onkeyup="searchContracts()" oninput="searchContracts()">
                </div>


            </div>
            
            <div class="controls-right">
                {% if user_permissions.is_admin %}
                <button onclick="downloadExcelFile()" class="btn btn-success">📊 تحميل Excel</button>
                <button onclick="downloadYearlyContracts()" class="btn btn-warning">📊 تحميل عقود السنة</button>
                {% endif %}
                <a href="/" class="btn btn-secondary">🔙 العودة للرئيسية</a>
            </div>
        </div>
        
        {% if contracts %}
        <div class="table-container">
            <table class="table" id="contractsTable">
                <thead>
                    <tr>
                        <th>رقم العقد</th>
                        <th>تاريخ العقد</th>
                        <th>اسم البائع</th>
                        <th>رقم تلفون البائع</th>
                        <th>اسم المشتري</th>
                        <th>رقم تلفون المشتري</th>
                        <th>نوع السيارة</th>
                        <th>رقم السيارة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="contractsTableBody">
                    {% for contract in contracts %}
                    <tr>
                        <td>{{ contract.serial_number }}</td>
                        <td>{{ contract.date }}</td>
                        <td>{{ contract.seller_name or 'غير محدد' }}</td>
                        <td>{{ contract.seller_phone or 'غير محدد' }}</td>
                        <td>{{ contract.buyer_name or 'غير محدد' }}</td>
                        <td>{{ contract.buyer_phone or 'غير محدد' }}</td>
                        <td>{{ contract.car_type or 'غير محدد' }}</td>
                        <td>{{ contract.car_number or 'غير محدد' }}</td>
                        <td class="actions-cell">
                            <a href="/view_contract_pdf/{{ contract.id }}" class="btn btn-info" title="عرض العقد كـ PDF في المتصفح" target="_blank">👁️ عرض</a>
                            {% if user_permissions.is_admin or user_permissions.can_download %}
                            <a href="/api/download_contract_word/{{ contract.id }}" class="btn btn-success" title="تحميل Word">📄 تحميل</a>
                            {% endif %}
                            {% if user_permissions.is_admin or user_permissions.can_edit %}
                            <a href="/edit_contract/{{ contract.id }}" class="btn btn-warning" title="تعديل العقد">✏️ تعديل</a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="pagination" id="pagination">
            <!-- سيتم إنشاء أزرار التنقل هنا بواسطة JavaScript -->
        </div>
        {% else %}
        <div class="no-contracts">
            <h3>لا توجد عقود مبرمة حتى الآن</h3>
            <p>سيتم عرض العقود هنا بمجرد إبرامها</p>
            <a href="/" class="btn btn-primary">إبرام عقد جديد</a>
        </div>
        {% endif %}
    </div>
    
    <script>
        let currentPage = 1;
        let recordsPerPage = 25;
        let allContracts = [];
        let filteredContracts = [];
        
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadContractsData();
            displayContracts();

            // إضافة مستمع للبحث الفوري
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchContracts();
                }, 300); // تأخير 300ms للبحث السلس
            });

            // إضافة مستمع لمفتاح Enter
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    clearTimeout(searchTimeout);
                    searchContracts();
                }
            });

            // إضافة اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                // Ctrl + F للتركيز على البحث
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    searchInput.focus();
                    searchInput.select();
                }



                // Ctrl + 1-8 لتغيير نوع البحث
                if (e.ctrlKey && e.key >= '1' && e.key <= '8') {
                    e.preventDefault();
                    const searchTypeSelect = document.getElementById('searchType');
                    const options = searchTypeSelect.options;
                    const index = parseInt(e.key) - 1;
                    if (index < options.length) {
                        searchTypeSelect.selectedIndex = index;
                        searchContracts();
                    }
                }
            });
        });
        
        function loadContractsData() {
            const tableBody = document.getElementById('contractsTableBody');
            const rows = tableBody.querySelectorAll('tr');

            allContracts = Array.from(rows).map(row => {
                const cells = row.querySelectorAll('td');
                return {
                    element: row,
                    serialNumber: (cells[0]?.textContent || '').trim(),
                    date: (cells[1]?.textContent || '').trim(),
                    sellerName: (cells[2]?.textContent || '').trim(),
                    sellerPhone: (cells[3]?.textContent || '').trim(),
                    buyerName: (cells[4]?.textContent || '').trim(),
                    buyerPhone: (cells[5]?.textContent || '').trim(),
                    carType: (cells[6]?.textContent || '').trim(),
                    carNumber: (cells[7]?.textContent || '').trim()
                };
            });

            filteredContracts = [...allContracts];
            updateSearchResults();
        }
        
        function searchContracts() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            const searchType = document.getElementById('searchType').value;

            if (searchTerm === '') {
                filteredContracts = [...allContracts];
            } else {
                // تحويل النص للبحث (إزالة المسافات الزائدة وتحويل للأحرف الصغيرة)
                const normalizedSearchTerm = normalizeSearchTerm(searchTerm);

                filteredContracts = allContracts.filter(contract => {
                    switch(searchType) {
                        case 'serial':
                            return matchesSearch(contract.serialNumber, normalizedSearchTerm);
                        case 'seller':
                            return matchesSearch(contract.sellerName, normalizedSearchTerm);
                        case 'buyer':
                            return matchesSearch(contract.buyerName, normalizedSearchTerm);
                        case 'seller_phone':
                            return matchesSearch(contract.sellerPhone, normalizedSearchTerm);
                        case 'buyer_phone':
                            return matchesSearch(contract.buyerPhone, normalizedSearchTerm);
                        case 'car_number':
                            return matchesSearch(contract.carNumber, normalizedSearchTerm);
                        case 'car_type':
                            return matchesSearch(contract.carType, normalizedSearchTerm);
                        case 'date':
                            return matchesSearch(contract.date, normalizedSearchTerm);
                        case 'all':
                        default:
                            return matchesSearch(contract.serialNumber, normalizedSearchTerm) ||
                                   matchesSearch(contract.sellerName, normalizedSearchTerm) ||
                                   matchesSearch(contract.buyerName, normalizedSearchTerm) ||
                                   matchesSearch(contract.sellerPhone, normalizedSearchTerm) ||
                                   matchesSearch(contract.buyerPhone, normalizedSearchTerm) ||
                                   matchesSearch(contract.carType, normalizedSearchTerm) ||
                                   matchesSearch(contract.carNumber, normalizedSearchTerm) ||
                                   matchesSearch(contract.date, normalizedSearchTerm);
                    }
                });
            }

            currentPage = 1;
            updateSearchResults();
            displayContracts();
        }
        
        // دالة لتطبيع نص البحث
        function normalizeSearchTerm(term) {
            return term.toLowerCase()
                      .replace(/\s+/g, ' ')  // استبدال المسافات المتعددة بمسافة واحدة
                      .trim();               // إزالة المسافات من البداية والنهاية
        }

        // دالة للمطابقة المحسنة
        function matchesSearch(text, searchTerm) {
            if (!text || !searchTerm) return false;

            const normalizedText = normalizeSearchTerm(text);

            // البحث الدقيق
            if (normalizedText.includes(searchTerm)) {
                return true;
            }

            // البحث بالكلمات المنفصلة
            const searchWords = searchTerm.split(' ').filter(word => word.length > 0);
            const textWords = normalizedText.split(' ').filter(word => word.length > 0);

            // التحقق من وجود جميع كلمات البحث
            if (searchWords.every(searchWord =>
                textWords.some(textWord => textWord.includes(searchWord))
            )) {
                return true;
            }

            // البحث بالأحرف الأولى (للأسماء)
            if (searchWords.length === 1 && searchWords[0].length >= 2) {
                const searchWord = searchWords[0];
                // البحث في بداية الكلمات
                if (textWords.some(textWord => textWord.startsWith(searchWord))) {
                    return true;
                }

                // البحث بالأحرف الأولى للكلمات
                const initials = textWords.map(word => word.charAt(0)).join('');
                if (initials.includes(searchWord)) {
                    return true;
                }
            }

            // البحث الضبابي للأرقام (للأرقام التسلسلية وأرقام السيارات)
            if (/^\d+$/.test(searchTerm)) {
                const textNumbers = normalizedText.match(/\d+/g) || [];
                return textNumbers.some(num => num.includes(searchTerm));
            }

            return false;
        }

        // دالة لتحديث نتائج البحث
        function updateSearchResults() {
            const totalContracts = allContracts.length;
            const filteredCount = filteredContracts.length;
            const searchTerm = document.getElementById('searchInput').value.trim();

            // تحديث عنوان الصفحة
            updatePageTitle(filteredCount, totalContracts, searchTerm);
        }

        // دالة لتحديث عنوان الصفحة
        function updatePageTitle(filteredCount, totalContracts, searchTerm) {
            const headerElement = document.querySelector('.header p');
            if (searchTerm === '') {
                headerElement.textContent = `إجمالي العقود: ${totalContracts}`;
            } else {
                headerElement.textContent = `نتائج البحث: ${filteredCount} من أصل ${totalContracts} عقد`;
            }
        }





        function changeRecordsPerPage() {
            recordsPerPage = parseInt(document.getElementById('recordsPerPage').value);
            currentPage = 1;
            displayContracts();
        }
        
        function displayContracts() {
            const tableBody = document.getElementById('contractsTableBody');
            const startIndex = (currentPage - 1) * recordsPerPage;
            const endIndex = startIndex + recordsPerPage;

            // إخفاء جميع الصفوف
            allContracts.forEach(contract => {
                contract.element.style.display = 'none';
            });

            // عرض الصفوف المطلوبة مع تمييز نتائج البحث
            const contractsToShow = filteredContracts.slice(startIndex, endIndex);
            contractsToShow.forEach(contract => {
                contract.element.style.display = '';
                highlightSearchResults(contract.element);
            });

            updatePagination();
            updateSearchResults();
        }

        // دالة لتمييز نتائج البحث
        function highlightSearchResults(row) {
            const searchTerm = document.getElementById('searchInput').value.trim();
            if (!searchTerm) return;

            const cells = row.querySelectorAll('td');
            cells.forEach(cell => {
                if (cell.classList.contains('actions-cell')) return; // تجاهل خلية الإجراءات

                const originalText = cell.textContent;
                const normalizedText = normalizeSearchTerm(originalText);
                const normalizedSearchTerm = normalizeSearchTerm(searchTerm);

                if (normalizedText.includes(normalizedSearchTerm)) {
                    // إضافة تمييز بصري خفيف
                    cell.style.backgroundColor = '#fff3cd';
                    cell.style.borderLeft = '3px solid #ffc107';
                } else {
                    // إزالة التمييز
                    cell.style.backgroundColor = '';
                    cell.style.borderLeft = '';
                }
            });
        }
        
        function updatePagination() {
            const totalPages = Math.ceil(filteredContracts.length / recordsPerPage);
            const paginationDiv = document.getElementById('pagination');
            
            let paginationHTML = `
                <div class="pagination-info">
                    عرض ${Math.min((currentPage - 1) * recordsPerPage + 1, filteredContracts.length)} إلى 
                    ${Math.min(currentPage * recordsPerPage, filteredContracts.length)} من 
                    ${filteredContracts.length} عقد
                </div>
            `;
            
            if (totalPages > 1) {
                paginationHTML += '<div>';
                
                // زر الصفحة السابقة
                if (currentPage > 1) {
                    paginationHTML += `<button class="btn btn-secondary" onclick="goToPage(${currentPage - 1})">السابق</button>`;
                }
                
                // أرقام الصفحات
                for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                    const activeClass = i === currentPage ? 'btn-primary' : 'btn-secondary';
                    paginationHTML += `<button class="btn ${activeClass}" onclick="goToPage(${i})">${i}</button>`;
                }
                
                // زر الصفحة التالية
                if (currentPage < totalPages) {
                    paginationHTML += `<button class="btn btn-secondary" onclick="goToPage(${currentPage + 1})">التالي</button>`;
                }
                
                paginationHTML += '</div>';
            }
            
            paginationDiv.innerHTML = paginationHTML;
        }
        
        function goToPage(page) {
            currentPage = page;
            displayContracts();
        }
        
        function downloadYearlyContracts() {
            // التوجه إلى صفحة تحميل عقود السنة
            window.location.href = '/admin/annual_export';
        }

        // دالة تحميل ملف Excel
        async function downloadExcelFile() {
            try {
                // إظهار رسالة تحميل
                const originalText = event.target.textContent;
                event.target.textContent = '⏳ جاري التحميل...';
                event.target.disabled = true;

                // طلب تحميل ملف Excel
                const response = await fetch('/api/export_contracts_excel');

                if (response.ok) {
                    // تحميل الملف
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;

                    // إنشاء اسم الملف مع التاريخ
                    const now = new Date();
                    const timestamp = now.getFullYear() +
                                    String(now.getMonth() + 1).padStart(2, '0') +
                                    String(now.getDate()).padStart(2, '0') + '_' +
                                    String(now.getHours()).padStart(2, '0') +
                                    String(now.getMinutes()).padStart(2, '0') +
                                    String(now.getSeconds()).padStart(2, '0');

                    a.download = `العقود_المبرمة_${timestamp}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    // إظهار رسالة نجاح
                    showSuccessMessage('تم تحميل ملف Excel بنجاح!');
                } else {
                    const errorData = await response.json();
                    showErrorMessage(errorData.error || 'فشل في تحميل ملف Excel');
                }
            } catch (error) {
                console.error('خطأ في تحميل Excel:', error);
                showErrorMessage('خطأ في تحميل ملف Excel');
            } finally {
                // إعادة تعيين النص والحالة
                event.target.textContent = originalText;
                event.target.disabled = false;
            }
        }

        // دالة إظهار رسالة نجاح
        function showSuccessMessage(message) {
            const alertDiv = document.createElement('div');
            alertDiv.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 1000;
                background: #28a745; color: white; padding: 15px 20px;
                border-radius: 5px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-size: 14px; max-width: 300px;
            `;
            alertDiv.textContent = message;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }

        // دالة إظهار رسالة خطأ
        function showErrorMessage(message) {
            const alertDiv = document.createElement('div');
            alertDiv.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 1000;
                background: #dc3545; color: white; padding: 15px 20px;
                border-radius: 5px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-size: 14px; max-width: 300px;
            `;
            alertDiv.textContent = message;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    </script>
        </div>
    </div>
</body>
</html>
